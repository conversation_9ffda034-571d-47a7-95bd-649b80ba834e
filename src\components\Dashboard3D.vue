<template>
  <div class="dashboard-3d">
    <canvas ref="canvasRef" class="three-canvas"></canvas>
    
    <!-- 覆盖层信息 -->
    <div class="overlay-info">
      <!-- 中央云平台标签 -->
      <div class="cloud-label" :style="cloudLabelStyle">
        <div class="label-content">
          <h4>云计算平台</h4>
          <p>运行状态：正常</p>
          <p>负载：{{ cloudLoad }}%</p>
        </div>
      </div>

      <!-- 服务器集群标签 -->
      <div 
        v-for="(cluster, index) in clusters" 
        :key="index"
        class="cluster-label"
        :style="cluster.labelStyle"
      >
        <div class="label-content">
          <h4>{{ cluster.name }}</h4>
          <p>服务器：{{ cluster.serverCount }}台</p>
          <p>状态：{{ cluster.status }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'

export default {
  name: 'Dashboard3D',
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      animationId: null,
      cloudLoad: 45,
      clusters: [
        { name: '数据库集群', serverCount: 12, status: '正常', labelStyle: { top: '15%', left: '15%' } },
        { name: '应用服务集群', serverCount: 24, status: '正常', labelStyle: { top: '15%', right: '15%' } },
        { name: '缓存集群', serverCount: 8, status: '正常', labelStyle: { bottom: '15%', left: '15%' } },
        { name: '消息队列集群', serverCount: 6, status: '正常', labelStyle: { bottom: '15%', right: '15%' } }
      ],
      cloudLabelStyle: {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      }
    }
  },
  mounted() {
    this.initThree()
    this.createScene()
    this.setupControls()
    this.animate()
    this.handleResize()
  },
  beforeUnmount() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initThree() {
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0x0a0e1a)

      // 创建相机
      const canvas = this.$refs.canvasRef
      this.camera = new THREE.PerspectiveCamera(
        75,
        canvas.clientWidth / canvas.clientHeight,
        0.1,
        1000
      )
      this.camera.position.set(0, 15, 25)
      this.camera.lookAt(0, 0, 0)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({
        canvas: canvas,
        antialias: true,
        alpha: true
      })
      this.renderer.setSize(canvas.clientWidth, canvas.clientHeight)
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    },

    setupControls() {
      // 简单的鼠标控制
      let isMouseDown = false
      let mouseX = 0
      let mouseY = 0
      let targetRotationX = 0
      let targetRotationY = 0
      let currentRotationX = 0
      let currentRotationY = 0

      const canvas = this.$refs.canvasRef

      canvas.addEventListener('mousedown', (event) => {
        isMouseDown = true
        mouseX = event.clientX
        mouseY = event.clientY
      })

      canvas.addEventListener('mousemove', (event) => {
        if (!isMouseDown) return

        const deltaX = event.clientX - mouseX
        const deltaY = event.clientY - mouseY

        targetRotationY += deltaX * 0.01
        targetRotationX += deltaY * 0.01

        // 限制垂直旋转角度
        targetRotationX = Math.max(-Math.PI / 3, Math.min(Math.PI / 3, targetRotationX))

        mouseX = event.clientX
        mouseY = event.clientY
      })

      canvas.addEventListener('mouseup', () => {
        isMouseDown = false
      })

      canvas.addEventListener('wheel', (event) => {
        event.preventDefault()
        const zoom = event.deltaY > 0 ? 1.1 : 0.9
        this.camera.position.multiplyScalar(zoom)

        // 限制缩放范围
        const distance = this.camera.position.length()
        if (distance < 15) {
          this.camera.position.normalize().multiplyScalar(15)
        } else if (distance > 50) {
          this.camera.position.normalize().multiplyScalar(50)
        }
      })

      // 在动画循环中更新相机位置
      this.updateCameraRotation = () => {
        currentRotationX += (targetRotationX - currentRotationX) * 0.05
        currentRotationY += (targetRotationY - currentRotationY) * 0.05

        const distance = 25
        this.camera.position.x = Math.sin(currentRotationY) * Math.cos(currentRotationX) * distance
        this.camera.position.y = Math.sin(currentRotationX) * distance + 15
        this.camera.position.z = Math.cos(currentRotationY) * Math.cos(currentRotationX) * distance
        this.camera.lookAt(0, 2, 0)
      }
    },

    createScene() {
      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0x1a2332, 0.4)
      this.scene.add(ambientLight)

      // 添加主光源
      const directionalLight = new THREE.DirectionalLight(0x00ffff, 1.2)
      directionalLight.position.set(10, 15, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      directionalLight.shadow.camera.near = 0.5
      directionalLight.shadow.camera.far = 50
      this.scene.add(directionalLight)

      // 添加点光源用于云平台照明
      const pointLight = new THREE.PointLight(0x00ffff, 1, 20)
      pointLight.position.set(0, 8, 0)
      this.scene.add(pointLight)

      // 添加四个角落的聚光灯
      const spotLightPositions = [
        { x: -12, z: -12 },
        { x: 12, z: -12 },
        { x: -12, z: 12 },
        { x: 12, z: 12 }
      ]

      spotLightPositions.forEach(pos => {
        const spotLight = new THREE.SpotLight(0x00ff00, 0.8, 25, Math.PI / 6, 0.3)
        spotLight.position.set(pos.x, 8, pos.z)
        spotLight.target.position.set(pos.x, 0, pos.z)
        spotLight.castShadow = true
        this.scene.add(spotLight)
        this.scene.add(spotLight.target)
      })

      // 创建中央云平台
      this.createCloudPlatform()

      // 创建四个角落的服务器集群
      this.createServerClusters()

      // 创建连接线
      this.createConnections()

      // 创建地面网格
      this.createFloorGrid()

      // 添加雾效
      this.scene.fog = new THREE.Fog(0x0a0e1a, 20, 60)
    },

    createCloudPlatform() {
      const group = new THREE.Group()

      // 主平台 - 多层设计
      const platformGeometry1 = new THREE.CylinderGeometry(3.5, 3.5, 0.3, 32)
      const platformGeometry2 = new THREE.CylinderGeometry(3, 3, 0.4, 32)
      const platformGeometry3 = new THREE.CylinderGeometry(2.5, 2.5, 0.5, 32)

      const platformMaterial = new THREE.MeshPhongMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8,
        emissive: 0x004444
      })

      const platform1 = new THREE.Mesh(platformGeometry1, platformMaterial)
      const platform2 = new THREE.Mesh(platformGeometry2, platformMaterial)
      const platform3 = new THREE.Mesh(platformGeometry3, platformMaterial)

      platform1.position.y = 1.5
      platform2.position.y = 2
      platform3.position.y = 2.5

      group.add(platform1, platform2, platform3)

      // 云朵形状 - 更复杂的结构
      const cloudGeometry = new THREE.SphereGeometry(0.8, 16, 16)
      const cloudMaterial = new THREE.MeshPhongMaterial({
        color: 0x00ccff,
        transparent: true,
        opacity: 0.7,
        emissive: 0x003366
      })

      // 中央云核心
      const centralCloud = new THREE.Mesh(
        new THREE.SphereGeometry(1.2, 20, 20),
        new THREE.MeshPhongMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.8,
          emissive: 0x004466
        })
      )
      centralCloud.position.y = 4
      group.add(centralCloud)

      // 周围云朵
      for (let i = 0; i < 8; i++) {
        const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial)
        const angle = (i / 8) * Math.PI * 2
        const radius = 1.8 + Math.sin(i) * 0.3
        cloud.position.set(
          Math.cos(angle) * radius,
          3.5 + Math.sin(i * 2) * 0.4,
          Math.sin(angle) * radius
        )
        cloud.scale.set(
          0.6 + Math.random() * 0.4,
          0.5 + Math.random() * 0.3,
          0.6 + Math.random() * 0.4
        )
        group.add(cloud)
      }

      // 多层发光环
      for (let i = 0; i < 3; i++) {
        const ringGeometry = new THREE.RingGeometry(3.5 + i * 0.5, 4 + i * 0.5, 32)
        const ringMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.2 - i * 0.05,
          side: THREE.DoubleSide
        })
        const ring = new THREE.Mesh(ringGeometry, ringMaterial)
        ring.rotation.x = -Math.PI / 2
        ring.position.y = 0.1 + i * 0.05
        group.add(ring)
      }

      // 添加粒子效果
      this.createParticleSystem(group)

      this.scene.add(group)
      this.cloudPlatform = group
    },

    createServerClusters() {
      const positions = [
        { x: -12, z: -12 }, // 左上
        { x: 12, z: -12 },  // 右上
        { x: -12, z: 12 },  // 左下
        { x: 12, z: 12 }    // 右下
      ]

      positions.forEach((pos, index) => {
        const cluster = this.createServerCluster(pos.x, pos.z)
        this.scene.add(cluster)
      })
    },

    createServerCluster(x, z) {
      const group = new THREE.Group()

      // 创建基础平台
      const platformGeometry = new THREE.CylinderGeometry(3, 3, 0.2, 8)
      const platformMaterial = new THREE.MeshPhongMaterial({
        color: 0x2a4a6b,
        transparent: true,
        opacity: 0.8,
        emissive: 0x001122
      })
      const platform = new THREE.Mesh(platformGeometry, platformMaterial)
      platform.position.set(x, 0.1, z)
      group.add(platform)

      // 创建服务器立方体 - 更真实的服务器外观
      const serverGeometry = new THREE.BoxGeometry(0.7, 1.5, 0.7)
      const serverMaterial = new THREE.MeshPhongMaterial({
        color: 0x4a90e2,
        emissive: 0x002244,
        shininess: 30
      })

      // 异常服务器材质
      const errorMaterial = new THREE.MeshPhongMaterial({
        color: 0xff4444,
        emissive: 0x440000,
        shininess: 30
      })

      // 高负载服务器材质
      const highLoadMaterial = new THREE.MeshPhongMaterial({
        color: 0xffaa00,
        emissive: 0x442200,
        shininess: 30
      })

      // 创建4x4的服务器阵列
      for (let i = 0; i < 4; i++) {
        for (let j = 0; j < 4; j++) {
          const random = Math.random()
          let material = serverMaterial
          if (random > 0.95) material = errorMaterial
          else if (random > 0.8) material = highLoadMaterial

          const server = new THREE.Mesh(serverGeometry, material)
          server.position.set(
            x + (i - 1.5) * 1.1,
            0.75,
            z + (j - 1.5) * 1.1
          )
          server.castShadow = true
          server.receiveShadow = true

          // 添加服务器指示灯
          const lightGeometry = new THREE.SphereGeometry(0.05, 8, 8)
          const lightMaterial = new THREE.MeshBasicMaterial({
            color: material === errorMaterial ? 0xff0000 :
                   material === highLoadMaterial ? 0xffaa00 : 0x00ff00,
            transparent: true,
            opacity: 0.8
          })
          const light = new THREE.Mesh(lightGeometry, lightMaterial)
          light.position.set(0, 0.7, 0.35)
          server.add(light)

          group.add(server)
        }
      }

      // 添加多层边框效果
      for (let i = 0; i < 2; i++) {
        const borderGeometry = new THREE.RingGeometry(3.5 + i * 0.3, 3.8 + i * 0.3, 8)
        const borderMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ff00,
          transparent: true,
          opacity: 0.4 - i * 0.1,
          side: THREE.DoubleSide
        })
        const border = new THREE.Mesh(borderGeometry, borderMaterial)
        border.rotation.x = -Math.PI / 2
        border.position.set(x, 0.05 + i * 0.02, z)
        group.add(border)
      }

      // 添加数据传输指示器
      const indicatorGeometry = new THREE.ConeGeometry(0.2, 0.8, 8)
      const indicatorMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.7
      })
      const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial)
      indicator.position.set(x, 2.5, z)
      group.add(indicator)

      return group
    },

    createParticleSystem(group) {
      const particleCount = 100
      const particles = new THREE.BufferGeometry()
      const positions = new Float32Array(particleCount * 3)

      for (let i = 0; i < particleCount; i++) {
        const angle = Math.random() * Math.PI * 2
        const radius = 2 + Math.random() * 3
        const height = 2 + Math.random() * 4

        positions[i * 3] = Math.cos(angle) * radius
        positions[i * 3 + 1] = height
        positions[i * 3 + 2] = Math.sin(angle) * radius
      }

      particles.setAttribute('position', new THREE.BufferAttribute(positions, 3))

      const particleMaterial = new THREE.PointsMaterial({
        color: 0x00ffff,
        size: 0.1,
        transparent: true,
        opacity: 0.8,
        blending: THREE.AdditiveBlending
      })

      const particleSystem = new THREE.Points(particles, particleMaterial)
      group.add(particleSystem)
      this.particles = particleSystem
    },

    createConnections() {
      const positions = [
        { x: -12, z: -12 },
        { x: 12, z: -12 },
        { x: -12, z: 12 },
        { x: 12, z: 12 }
      ]

      positions.forEach((pos, index) => {
        // 创建弧形连接线
        const curve = new THREE.QuadraticBezierCurve3(
          new THREE.Vector3(pos.x, 1, pos.z),
          new THREE.Vector3(pos.x * 0.5, 6, pos.z * 0.5),
          new THREE.Vector3(0, 3, 0)
        )

        const points = curve.getPoints(50)
        const geometry = new THREE.BufferGeometry().setFromPoints(points)

        // 主连接线
        const material = new THREE.LineBasicMaterial({
          color: 0x00ff00,
          transparent: true,
          opacity: 0.8
        })

        const line = new THREE.Line(geometry, material)
        this.scene.add(line)

        // 发光效果
        const glowMaterial = new THREE.LineBasicMaterial({
          color: 0x00ff00,
          transparent: true,
          opacity: 0.3,
          linewidth: 3
        })

        const glowLine = new THREE.Line(geometry, glowMaterial)
        this.scene.add(glowLine)

        // 数据流动效果
        this.createDataFlow(curve, index)
      })
    },

    createDataFlow(curve, index) {
      const flowGeometry = new THREE.SphereGeometry(0.1, 8, 8)
      const flowMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8
      })

      const flowSphere = new THREE.Mesh(flowGeometry, flowMaterial)
      this.scene.add(flowSphere)

      // 存储用于动画
      if (!this.dataFlows) this.dataFlows = []
      this.dataFlows.push({
        sphere: flowSphere,
        curve: curve,
        progress: Math.random(),
        speed: 0.01 + Math.random() * 0.02
      })
    },

    createFloorGrid() {
      // 主网格
      const gridHelper = new THREE.GridHelper(50, 50, 0x004466, 0x002233)
      gridHelper.position.y = -0.1
      this.scene.add(gridHelper)

      // 添加发光的十字线
      const crossGeometry = new THREE.BufferGeometry()
      const crossPoints = [
        new THREE.Vector3(-25, 0, 0),
        new THREE.Vector3(25, 0, 0),
        new THREE.Vector3(0, 0, -25),
        new THREE.Vector3(0, 0, 25)
      ]
      crossGeometry.setFromPoints(crossPoints)

      const crossMaterial = new THREE.LineBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.6
      })

      const crossLines = new THREE.LineSegments(crossGeometry, crossMaterial)
      this.scene.add(crossLines)

      // 添加中心圆环
      const ringGeometry = new THREE.RingGeometry(8, 8.2, 64)
      const ringMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.4,
        side: THREE.DoubleSide
      })
      const centerRing = new THREE.Mesh(ringGeometry, ringMaterial)
      centerRing.rotation.x = -Math.PI / 2
      centerRing.position.y = 0.01
      this.scene.add(centerRing)
    },

    animate() {
      this.animationId = requestAnimationFrame(this.animate)

      const time = Date.now() * 0.001

      // 更新相机控制
      if (this.updateCameraRotation) {
        this.updateCameraRotation()
      }

      // 旋转云平台
      if (this.cloudPlatform) {
        this.cloudPlatform.rotation.y += 0.005

        // 云朵上下浮动
        this.cloudPlatform.children.forEach((child, index) => {
          if (child.geometry && child.geometry.type === 'SphereGeometry') {
            child.position.y += Math.sin(time * 2 + index) * 0.002
          }
        })
      }

      // 粒子动画
      if (this.particles) {
        this.particles.rotation.y += 0.01
        const positions = this.particles.geometry.attributes.position.array
        for (let i = 0; i < positions.length; i += 3) {
          positions[i + 1] += Math.sin(time * 3 + i) * 0.01
        }
        this.particles.geometry.attributes.position.needsUpdate = true
      }

      // 数据流动动画
      if (this.dataFlows) {
        this.dataFlows.forEach(flow => {
          flow.progress += flow.speed
          if (flow.progress > 1) flow.progress = 0

          const point = flow.curve.getPoint(flow.progress)
          flow.sphere.position.copy(point)

          // 添加发光效果
          flow.sphere.material.opacity = 0.8 + Math.sin(time * 10) * 0.2
        })
      }

      // 服务器指示灯闪烁
      this.scene.traverse((child) => {
        if (child.material && child.geometry && child.geometry.type === 'SphereGeometry' && child.parent.geometry) {
          child.material.opacity = 0.8 + Math.sin(time * 5 + child.position.x) * 0.2
        }
      })

      // 更新云负载数据
      this.cloudLoad = Math.round(45 + Math.sin(time) * 10)

      this.renderer.render(this.scene, this.camera)
    },

    handleResize() {
      window.addEventListener('resize', () => {
        const canvas = this.$refs.canvasRef
        if (canvas && this.camera && this.renderer) {
          this.camera.aspect = canvas.clientWidth / canvas.clientHeight
          this.camera.updateProjectionMatrix()
          this.renderer.setSize(canvas.clientWidth, canvas.clientHeight)
        }
      })
    }
  }
}
</script>

<style scoped>
.dashboard-3d {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.three-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.overlay-info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.cloud-label,
.cluster-label {
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 255, 255, 0.5);
  border-radius: 8px;
  padding: 10px;
  backdrop-filter: blur(5px);
  pointer-events: auto;
  transition: all 0.3s ease;
}

.cloud-label:hover,
.cluster-label:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: #00ffff;
  transform: scale(1.05);
}

.label-content h4 {
  color: #00ffff;
  margin-bottom: 5px;
  font-size: 14px;
}

.label-content p {
  color: #cccccc;
  font-size: 12px;
  margin: 2px 0;
}
</style>
